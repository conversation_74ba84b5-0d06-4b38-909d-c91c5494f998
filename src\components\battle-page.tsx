"use client";

import { Dispatch, SetStateAction, useEffect, useState, useRef, useCallback } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, Heart, Users, ArrowLeft, Volume2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { formatTime, roundNumber, getCurrentWordForMatch, fetchPlayersFromDatabase, initiateSpellingTimer, performHeartbeatSyncBattle, updatePendingPlayers, updatePlayersHasAnswered } from "@/utils/battle-utils";
import { Player, User, Match, GameStateType, Difficulty, currentWord } from "@/interfaces/interfaces";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { createClient } from "../../supabase/client";
import React from "react";
import { getWordAudioPath, playWordAudio } from "@/utils/audio-utils";
import { setCurrentWordId, updateMatchCurrentState } from "@/utils/database";
import { calculateTimeLeft, resetStartTime } from "@/utils/waiting-utils";

interface BattlePageProps {
  roomName: string;
  roomColor: string;
  roomBgColor: string;
  matchId?: string;
  clientGameState: GameStateType;
  setClientGameState: Dispatch<SetStateAction<GameStateType>>;
  clientCurrentWordId: number;
  setClientCurrentWordId: Dispatch<SetStateAction<number>>;
  currentWord: {
    text: string;
    difficulty: Difficulty;
    audioUrl?: string;
  };
  setCurrentWord: Dispatch<SetStateAction<{
    text: string;
    difficulty: Difficulty;
    audioUrl?: string;
  }>>;
  currentUser: User; // Add currentUser to props
  currentMatch: Match | null; // Add currentMatch to props
}

export default function BattlePage({
  roomName,
  roomColor,
  roomBgColor,
  matchId,
  clientGameState,
  setClientGameState,
  clientCurrentWordId,
  setClientCurrentWordId,
  currentWord,
  setCurrentWord,
  currentUser, // Destructure currentUser
  currentMatch
}: BattlePageProps) {
  const supabase = createClient();
  const router = useRouter();

  const isFirstMount = React.useRef(true);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState(true);
  const [userAnswer, setUserAnswer] = useState("");
  const [playerData, setPlayerData] = useState<Player[]>([]);
  const [timeLeft, setTimeLeft] = useState(15);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [matchData, setMatchData] = useState<any>(null); // State to store match data
  const [countdownActive, setCountdownActive] = useState(false);
  const [initialTime, setInitialTime] = useState(15);
  const [correctAnswer, setCorrectAnswer] = useState("");
  const [didItPlayed, setDidItPlayed] = useState(false);

  const handleCheckAnswer = async () => {
    const isCorrect = userAnswer.trim().toLowerCase() === correctAnswer.toLowerCase();

    if (isCorrect) {
      toast.success("Correct answer!");
      
      const currentPlayer = playerData.find(p => p.id === currentUser.id);
      if (!currentPlayer) {
        toast.error("Player not found.");
        return;
      }

      let updatedScore = currentPlayer.score || 0;
      let updatedLives = currentPlayer.lives || 0;
      let lastAnswerStatus = "";

      updatedScore += 10; // Increase score by 10 for correct answer
      lastAnswerStatus = "correct";

      // Update player in database
      const { error: updateError } = await supabase
        .from('match_players')
        .update({ 
          score: updatedScore, 
          lives: updatedLives,
          lastAnswer: lastAnswerStatus,
          has_answered: true // Mark player as having answered
        })
        .eq('player_id', currentUser.id)
        .eq('match_id', matchId);

      if (updateError) {
        console.error("Error updating player data:", updateError);
        toast.error("Failed to update player data.");
        return;
      }

      // Update local state
      setPlayerData(prevData => 
        prevData.map(p => 
          p.id === currentUser.id 
            ? { ...p, score: updatedScore, lives: updatedLives, lastAnswer: lastAnswerStatus, has_answered: true } 
            : p
        )
      );

      setUserAnswer("");
      setClientGameState("break");
    } else {
      toast.error("Incorrect answer. Try again!");
      
      const currentPlayer = playerData.find(p => p.id === currentUser.id);
      if (!currentPlayer) {
        toast.error("Player not found.");
        return;
      }

      let updatedScore = currentPlayer.score || 0;
      let updatedLives = currentPlayer.lives || 0;
      let lastAnswerStatus = "";

      updatedLives = Math.max(0, updatedLives - 1); // Decrease lives by 1, minimum 0
      lastAnswerStatus = "incorrect";

      // Update player in database
      const { error: updateError } = await supabase
        .from('match_players')
        .update({ 
          score: updatedScore, 
          lives: updatedLives,
          lastAnswer: lastAnswerStatus,
          has_answered: true // Mark player as having answered
        })
        .eq('player_id', currentUser.id)
        .eq('match_id', matchId);

      if (updateError) {
        console.error("Error updating player data:", updateError);
        toast.error("Failed to update player data.");
        return;
      }

      // Update local state
      setPlayerData(prevData => 
        prevData.map(p => 
          p.id === currentUser.id 
            ? { ...p, score: updatedScore, lives: updatedLives, lastAnswer: lastAnswerStatus, has_answered: true } 
            : p
        )
      );

      setUserAnswer("");
      setClientGameState("break");
    }
  }

  const handleLeaveRoom = async () => {
    try {
      const { error: deleteError } = await supabase
        .from('match_players')
        .delete()
        .eq('player_id', currentUser.id);

      if (deleteError) {
        console.error("Error removing player from match_players:", deleteError);
        toast.error("Failed to leave room: Could not remove you from the match.");
        return;
      }

      toast.success("You have left the room.");
      console.clear();
      window.location.href = '/dashboard';
    } catch (error) {
      console.error("Exception leaving room:", error);
      toast.error("An unexpected error occurred while leaving the room.");
    }
  };

  const fetchPlayers = async () => {
    if (matchId) {
      const fetchedPlayers = await fetchPlayersFromDatabase(roomName, matchId);
      setPlayerData(fetchedPlayers || []);
    }
  }

  // Function to play audio for the current word
  const playCurrentWordAudio = async (word: currentWord) => {
    if (!matchId || !word.text) return;

    setIsAudioPlaying(true);

    try {
      const audioFinished = await playWordAudio(word.text, word.difficulty);

      console.log('Audio finished:', audioFinished);

      // If audio finished playing successfully, run the codeblock
      if (audioFinished) {
        setIsAudioPlaying(false);
        if (matchId) {
          initiateSpellingTimer(
            matchId,
            setCountdownActive,
            setTimeLeft,
            initialTime
          );
        }
        inputRef.current?.focus();
        setDidItPlayed(true);
      } else {
        // Handle audio playback failure
        console.error('Audio playback failed');
        setIsAudioPlaying(false);
        // Still allow the game to continue even if audio fails
        if (matchId) {
          initiateSpellingTimer(
            matchId,
            setCountdownActive,
            setTimeLeft,
            initialTime
          );
        }
        inputRef.current?.focus();
        setDidItPlayed(true);
        timer();
      }
    } catch (error) {
      console.error('Error in audio playback:', error);
      setIsAudioPlaying(false);
      // Still allow the game to continue even if audio fails
      if (matchId) {
        initiateSpellingTimer(
          matchId,
          setCountdownActive,
          setTimeLeft,
          initialTime
        );
        timer();
      }
      inputRef.current?.focus();
      setDidItPlayed(true);
    }
  };

  useEffect(() => {
    if (!matchId) return;
    const subscription = supabase
      .channel('match_changes')
      .on(
        'postgres_changes',
        { 
          event: 'UPDATE',
          schema: 'public',
          table: 'matches',
          filter: `id=eq.${matchId}`
        },
        async (payload) => {
          if (payload.new.current_word_id !== clientCurrentWordId) {
            try {
              const { data: wordData, error: wordError } = await supabase
                .from('words')
                .select('*')
                .eq('id', payload.new.current_word_id)
                .single();

              if (wordError) throw wordError;

              if (wordData) {
                setCurrentWord({
                  text: wordData.text,
                  difficulty: wordData.difficulty,
                  audioUrl: wordData.audio_url
                });
              }
              setClientCurrentWordId(wordData.id);
            } catch (error) {
              console.error("Error fetching word data:", error);
            }
          }
        }
      )
      .subscribe();

    const playerSubscription = supabase
      .channel('match_players_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'match_players',
          filter: `match_id=eq.${matchId}`
        },
        async (payload) => {
          // Update the specific player in the playerData state
          setPlayerData(prevData =>
            prevData.map(player =>
              player.id === payload.new.player_id
                ? {
                    ...player,
                    score: payload.new.score,
                    lives: payload.new.lives,
                    lastAnswer: payload.new.lastAnswer,
                    has_answered: payload.new.has_answered
                  }
                : player
            )
          );
        }
      )
      .subscribe();

    const heartbeatInterval = setInterval(
      async () => { // Made the callback async
        if (!countdownActive) {
          const startTime = calculateTimeLeft(await performHeartbeatSyncBattle( // Await the function call
            supabase, 
            matchId, 
            countdownActive, 
            setCountdownActive, 
            setTimeLeft, 
            initialTime
          ))
          console.log(startTime)
          if (startTime <= 10 && startTime !== 0) {
            console.log("cleared", startTime)
            clearInterval(heartbeatInterval)
          }
        }
      }, 3000);

    return () => {
      subscription.unsubscribe();
      playerSubscription.unsubscribe();
      clearInterval(heartbeatInterval);
    };
  }, [matchId, supabase]);

  const timer = async () => {
    if (!matchId || !countdownActive) return;

    let timer: NodeJS.Timeout | undefined;
    if (!isAudioPlaying && !currentMatch?.start_time) {
      try {
          const { data, error } = await supabase
            .from('matches')
            .select('start_time')
            .eq('id', matchId)
            .single();

          if (error || !data || !data.start_time) {
            console.error("Error fetching match for countdown:", error);
            clearInterval(timer);
            return;
          }

          const newTimeLeft = calculateTimeLeft(data.start_time);
          setTimeLeft(newTimeLeft);

          // Check if all players have answered during spelling phase
          if (playerData.length > 0 && playerData.every(player => player.has_answered)) {
            await updateMatchCurrentState(matchId, "spelling", "break");
            await updatePendingPlayers(matchId); // Updates all "pending" lastAnswer into "incorrect"
            await updatePlayersHasAnswered(matchId); // Updates all players "has_answered" into false
            await resetStartTime(matchId); // Reuse start_time for break timer
            setClientGameState("break");
            setCountdownActive(false);
            clearInterval(timer);
            return; // Exit early as state has changed
          }

          if (newTimeLeft <= 0) {
            await updateMatchCurrentState(matchId, "spelling", "break");
            await updatePendingPlayers(matchId); // Updates all "pending" lastAnswer into "incorrect"
            await updatePlayersHasAnswered(matchId); // Updates all players "has_answered" into false
            await resetStartTime(matchId); // This sets the start time into "null"
            setClientGameState("break");
            setCountdownActive(false)
            clearInterval(timer);
          }
        } catch (error) {
          console.error("Error in countdown timer:", error);
        }
    }
  }

  // timer
  // useEffect(() => {
  //   let timer: NodeJS.Timeout | undefined;

  //   if (!matchId || !countdownActive) {
  //     return;
  //   }

  //   if (!isAudioPlaying) {
  //     timer = setInterval(async () => {
  //       try {
  //         const { data, error } = await supabase
  //           .from('matches')
  //           .select('start_time')
  //           .eq('id', matchId)
  //           .single();

  //         if (error || !data || !data.start_time) {
  //           console.error("Error fetching match for countdown:", error);
  //           clearInterval(timer);
  //           return;
  //         }

  //         const newTimeLeft = calculateTimeLeft(data.start_time);
  //         setTimeLeft(newTimeLeft);

  //         // Check if all players have answered during spelling phase
  //         if (playerData.length > 0 && playerData.every(player => player.has_answered)) {
  //           await updateMatchCurrentState(matchId, "spelling", "break");
  //           await updatePendingPlayers(matchId); // Updates all "pending" lastAnswer into "incorrect"
  //           await updatePlayersHasAnswered(matchId); // Updates all players "has_answered" into false
  //           await resetStartTime(matchId); // Reuse start_time for break timer
  //           setClientGameState("break");
  //           setCountdownActive(false);
  //           clearInterval(timer);
  //           return; // Exit early as state has changed
  //         }

  //         if (newTimeLeft <= 0) {
  //           await updateMatchCurrentState(matchId, "spelling", "break");
  //           await updatePendingPlayers(matchId); // Updates all "pending" lastAnswer into "incorrect"
  //           await updatePlayersHasAnswered(matchId); // Updates all players "has_answered" into false
  //           await resetStartTime(matchId); // This sets the start time into "null"
  //           setClientGameState("break");
  //           setCountdownActive(false)
  //           clearInterval(timer);
  //         }
  //       } catch (error) {
  //         console.error("Error in countdown timer:", error);
  //       }
  //     }, 1000);
  //   }

  //   return () => {
  //     if (timer) clearInterval(timer);
  //   };
  // }, [isAudioPlaying, countdownActive, initiateSpellingTimer, matchId, setCountdownActive, setIsAudioPlaying, playerData, clientGameState]);

  // Check if current player has answered
  useEffect(() => {
    const currentPlayer = playerData.find(player => player.id === currentUser.id);
    if (currentPlayer && currentPlayer.has_answered === true) {
      setClientGameState("break")
    }
  }, [playerData]);

  // First mount
  useEffect(() => {
    console.log(currentMatch?.current_state)

    if (!matchId) return;
    
    (async () => {
      if (isFirstMount.current) {
        const wordData = await getCurrentWordForMatch(matchId);
        setCorrectAnswer(wordData.text);
        setCurrentWord(wordData);
        console.log(wordData.text)
        fetchPlayers();
        
        // Play audio on first mount
        playCurrentWordAudio(wordData);
        
        // Setup replay check
        // const replayCheck = setTimeout(() => {
        //   if (!didItPlayed) {
        //     playCurrentWordAudio(wordData);
        //     console.log("Replaying audio");
        //   }
        // }, 6000);
        
        isFirstMount.current = false;
      }
    })();
  }, [])

  return (
    <div className="bg-amber-50/30 min-h-screen w-full">
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-8">        
        {/* Header */}
        <header className="flex flex-col gap-3 sm:gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 sm:gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="text-amber-800 hover:text-amber-900 hover:bg-amber-100 h-8 w-8 sm:h-9 sm:w-9"
                onClick={() => handleLeaveRoom()}
              >
                <ArrowLeft size={18} />
              </Button>
              <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-amber-900">
                {roomName} Battle
              </h1>
            </div>
            <div className={cn("px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm", roomBgColor)}>
              <span className={cn("font-medium", roomColor)}>{roomName}</span>
            </div>
          </div>
        </header>

        {/* Timer */}
        <div className="bg-amber-100 text-sm p-3 px-4 rounded-lg text-amber-800 flex flex-col gap-2 border border-amber-200 sticky top-[10px] z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock size={14} />
              <span>
                {clientGameState === "spelling" && `Time left: ${formatTime(timeLeft)}`}
                {clientGameState === "break" && "Break time!"}
                {clientGameState === "gameOver" && "Game Over!"}
                {clientGameState === "results" && "Final Results"}
              </span>
            </div>
            <span>Round {roundNumber}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          {/* Main game area - conditionally rendered based on clientGameState */}
          <div className="md:col-span-2 bg-white rounded-xl p-6 border shadow-sm">
            {clientGameState === "spelling" && (
              <div className="flex flex-col items-center justify-center py-8">
                <h2 className="text-xl font-semibold text-amber-900 mb-6">
                  Spell the word:
                </h2>

                <div className="flex flex-col items-center gap-2 mb-8">
                  <div className="flex items-center gap-2">
                    {isAudioPlaying ? (
                      <div className="text-center">
                        <div className="h-12 w-48 bg-amber-100 animate-pulse rounded-lg mb-2"></div>
                        <p className="text-sm text-amber-600">Loading word...</p>
                      </div>
                    ) : (
                      <>
                        <div className="text-4xl font-bold text-amber-800">
                          ??????
                        </div>
                      </>
                    )}
                  </div>

                  {isAudioPlaying ? (
                    <div className="h-6 w-24 bg-amber-100 animate-pulse rounded-full mt-2"></div>
                  ) : (
                    <div className={cn(
                      "text-xs px-2 py-1 rounded-full mt-2",
                      currentWord.difficulty === "easy" ? "bg-green-100 text-green-800" :
                      currentWord.difficulty === "medium" ? "bg-blue-100 text-blue-800" :
                      "bg-amber-100 text-amber-800"
                    )}>
                      {currentWord.difficulty.charAt(0).toUpperCase() + currentWord.difficulty.slice(1)} difficulty
                    </div>
                  )}
                </div>

                <div className="w-full max-w-md">
                  <div className={cn(
                    "text-center mb-3 py-2 px-4 rounded-md text-sm font-medium",
                    isAudioPlaying ? "bg-yellow-100 text-yellow-800" : "bg-green-100 text-green-800"
                  )}>
                    {isAudioPlaying ? 
                      "Now listen to the word!" :
                      "Now spell the word!"
                    }
                  </div>

                  <div className="w-full mb-3 h-1 bg-amber-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-amber-600 transition-all duration-1000" 
                      style={{ width: `${(timeLeft / initialTime) * 100}%` }}
                    />
                  </div>
                  <form
                    className="flex gap-2"
                    onSubmit={(e) => {
                      e.preventDefault();
                      handleCheckAnswer();
                    }}
                  >
                    <input
                      ref={inputRef}
                      type="text"
                      className="flex h-10 w-full rounded-md border border-amber-300 bg-white px-3 py-2 text-sm placeholder:text-amber-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-amber-400 focus-visible:ring-offset-2"
                      placeholder="Type the word here..."
                      value={userAnswer}
                      onChange={(e) => setUserAnswer(e.target.value)}
                      disabled={isAudioPlaying}
                    />
                    <Button
                      className="bg-amber-600 hover:bg-amber-700 text-white"
                      type="submit"
                      disabled={isAudioPlaying}
                    >
                      Submit
                    </Button>
                  </form>
                </div>
                
                <div className="mt-6 flex justify-center">
                  <div className="flex gap-2">
                    <Heart size={24} className="text-red-500" fill="#ef4444" />
                    <Heart size={24} className="text-red-500" fill="#ef4444" />
                    <Heart size={24} className="text-red-500" fill="#ef4444" />
                  </div>
                </div>
              </div>
            )}

            {clientGameState === "break" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-4">
                  <div className="text-4xl font-bold text-amber-600 mb-2">
                    Break Time!
                  </div>
                  <p className="text-amber-700">Next round starting soon...</p>
                </div>

                {/* Current standings */}
                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm mb-4">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Current Standings</h3>
                  <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                    <span className="font-medium text-amber-800">Player</span>
                    <span className="font-medium text-amber-800 text-center">Status</span>
                    <span className="font-medium text-amber-800 text-right">Score</span>
                  </div>

                  {playerData.map((player) => (
                    <div
                      key={player.id}
                      className={cn(
                        "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                        ((player.lives || 0) <= 0 || player.is_spectator) && "bg-gray-100"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Avatar className="h-7 w-7">
                          <AvatarFallback className={cn("text-xs", "bg-amber-200 text-amber-800")}>
                            {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm text-amber-800">
                            {player.display_name}
                          </div>
                          <div className="flex mt-1">
                            {Array.from({ length: player.lives || 0 }).map((_, i) => (
                              <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-center">
                        <span className={cn(
                          "text-xs px-2 py-1 rounded-full",
                          player.lastAnswer === "correct" ? "bg-green-100 text-green-800" :
                          player.lastAnswer === "incorrect" ? "bg-red-100 text-red-800" :
                          "bg-amber-100 text-amber-800",
                          ((player.lives || 0) <= 0 || player.is_spectator) && "hidden" // Hide status for spectators
                        )}>
                          {player.lastAnswer}
                        </span>
                      </div>
                      <div className="font-medium text-amber-800 text-right">
                        {player.score || 0}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {clientGameState === "gameOver" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-red-600 mb-2">
                    Game Over!
                  </div>
                  <p className="text-amber-700">The battle has ended</p>
                </div>

                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm mb-4">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Final Scores</h3>
                  <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                    <span className="font-medium text-amber-800">Player</span>
                    <span className="font-medium text-amber-800 text-center">Status</span>
                    <span className="font-medium text-amber-800 text-right">Score</span>
                  </div>

                  {playerData.map((player) => (
                    <div
                      key={player.id}
                      className={cn(
                        "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                        ((player.lives || 0) <= 0 || player.is_spectator) && "bg-gray-100"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Avatar className="h-7 w-7">
                          <AvatarFallback className={cn("text-xs", "bg-amber-200 text-amber-800")}>
                            {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm text-amber-800">
                            {player.display_name}
                          </div>
                          <div className="flex mt-1">
                            {Array.from({ length: player.lives || 0 }).map((_, i) => (
                              <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-center">
                        <span className={cn(
                          "text-xs px-2 py-1 rounded-full",
                          player.lastAnswer === "correct" ? "bg-green-100 text-green-800" :
                          player.lastAnswer === "incorrect" ? "bg-red-100 text-red-800" :
                          "bg-amber-100 text-amber-800",
                          ((player.lives || 0) <= 0 || player.is_spectator) && "hidden" // Hide status for spectators
                        )}>
                          {player.lastAnswer}
                        </span>
                      </div>
                      <div className="font-medium text-amber-800 text-right">
                        {player.score || 0}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {clientGameState === "results" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-green-600 mb-2">
                    Battle Results
                  </div>
                  <p className="text-amber-700">Final standings</p>
                </div>

                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Rankings</h3>
                  <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                    <span className="font-medium text-amber-800">Player</span>
                    <span className="font-medium text-amber-800 text-center">Status</span>
                    <span className="font-medium text-amber-800 text-right">Score</span>
                  </div>

                  {[...playerData]
                    .sort((a, b) => (b.score || 0) - (a.score || 0))
                    .map((player, index) => (
                      <div
                        key={player.id}
                        className={cn(
                          "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                          ((player.lives || 0) <= 0 || player.is_spectator) && "bg-gray-100"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 flex items-center justify-center bg-amber-200 text-amber-800 rounded-full text-xs font-bold">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium text-sm text-amber-800">
                              {player.display_name}
                            </div>
                            <div className="flex mt-1">
                              {Array.from({ length: player.lives || 0 }).map((_, i) => (
                                <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-center">
                          <span className={cn(
                            "text-xs px-2 py-1 rounded-full",
                            player.lastAnswer === "correct" ? "bg-green-100 text-green-800" :
                            player.lastAnswer === "incorrect" ? "bg-red-100 text-red-800" :
                            "bg-amber-100 text-amber-800",
                            ((player.lives || 0) <= 0 || player.is_spectator) && "hidden" // Hide status for spectators
                          )}>
                            {player.lastAnswer}
                          </span>
                        </div>
                        <div className="font-medium text-amber-800 text-right">
                          {player.score || 0}
                        </div>
                      </div>
                    ))}
                </div>

                <Button className="mt-6 bg-amber-600 hover:bg-amber-700 text-white">
                  Play Again
                </Button>
              </div>
            )}
          </div>

          {/* Players section */}
          <div className="bg-white rounded-xl p-4 sm:p-6 border shadow-sm">
            <h2 className="text-lg sm:text-xl font-semibold text-amber-900 mb-4 flex items-center gap-2">
              <Users className="h-5 w-5 text-amber-600" />
              Active Players ({playerData.filter(p => (p.lives || 0) > 0 && !p.is_spectator).length})
            </h2>
            <div className="flex flex-col gap-3 mb-6">
              {playerData.filter(p => (p.lives || 0) > 0 && !p.is_spectator).map((player) => (
                <div key={player.id} className="flex items-center justify-between p-2 rounded-lg hover:bg-amber-50">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback className="bg-amber-200 text-amber-800 text-sm">
                        {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-amber-900 text-base">
                        {player.display_name}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex">
                      {Array.from({ length: player.lives || 0 }).map((_, i) => (
                        <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                      ))}
                    </div>
                    <span className="font-medium text-amber-800 ml-2">
                      {player.score || 0}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {playerData.filter(p => (p.lives || 0) <= 0 || p.is_spectator).length > 0 && (
              <>
                <h3 className="text-lg font-medium text-amber-900 mb-2 mt-4">
                  Spectators ({playerData.filter(p => (p.lives || 0) <= 0 || p.is_spectator).length})
                </h3>
                <div className="flex flex-col gap-2">
                  {playerData.filter(p => (p.lives || 0) <= 0 || p.is_spectator).map((player) => (
                    <div key={player.id} className="flex items-center gap-3 p-2 rounded-lg bg-amber-50/50">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-amber-100 text-amber-800 text-sm">
                          {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-amber-700 text-base">
                          {player.display_name}
                        </p>
                        <span className="text-xs text-amber-500">
                          Eliminated
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Game info section */}
        <div className="bg-white rounded-xl p-6 border shadow-sm">
          <h2 className="text-xl font-semibold text-amber-900 mb-4">
            Battle Information
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">Rules</h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Type the word correctly within 15 seconds</li>
                <li>Each correct answer earns you 10 points</li>
                <li>Three mistakes and you're eliminated</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">Tips</h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Listen carefully to the pronunciation</li>
                <li>Watch for common spelling patterns</li>
                <li>Double-check before submitting</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">
                Difficulty: {roomName}
              </h3>
              <p className="text-sm text-amber-700">
                {roomName === "Easy" && "Simple words with common spelling patterns"}
                {roomName === "Medium" && "Moderate difficulty with some tricky words"}
                {roomName === "Hard" && "Challenging words that test your skills"}
              </p>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
}
